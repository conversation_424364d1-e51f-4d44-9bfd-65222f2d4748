            </main>
        </div>
    </div>

    <!-- Sidebar Overlay for Mobile -->
    <div class="sidebar-overlay" id="sidebarOverlay"></div>

    <!-- Loading Overlay -->
    <div id="loadingOverlay" class="loading-overlay" style="display: none;">
        <div class="loading-spinner">
            <div class="spinner-border text-primary" role="status">
                <span class="visually-hidden">Loading...</span>
            </div>
            <div class="loading-text">Loading...</div>
        </div>
    </div>

    <!-- Scripts -->
    <!-- jQuery -->
    <script src="https://code.jquery.com/jquery-3.7.1.min.js" crossorigin="anonymous"></script>
    
    <!-- Bootstrap 5.3 JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js" crossorigin="anonymous"></script>
    
    <!-- DataTables JS -->
    <script src="https://cdn.datatables.net/1.13.7/js/jquery.dataTables.min.js" crossorigin="anonymous"></script>
    <script src="https://cdn.datatables.net/1.13.7/js/dataTables.bootstrap5.min.js" crossorigin="anonymous"></script>
    
    <!-- Chart.js (if needed) -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js@4.4.0/dist/chart.min.js" crossorigin="anonymous"></script>
    
    <!-- Custom Admin Scripts -->
    <script src="assets/js/custom_script.js"></script>
    
    <!-- Performance Script -->
    <?php if (file_exists('assets/js/performance.js')): ?>
        <script src="assets/js/performance.js"></script>
    <?php endif; ?>

    <!-- Page-specific scripts -->
    <?php if (isset($pageScripts) && is_array($pageScripts)): ?>
        <?php foreach ($pageScripts as $script): ?>
            <script src="<?php echo htmlspecialchars($script); ?>"></script>
        <?php endforeach; ?>
    <?php endif; ?>

    <!-- Inline JavaScript -->
    <script>
        // Global admin configuration
        window.AdminConfig = {
            baseUrl: '<?php echo rtrim(dirname($_SERVER['PHP_SELF']), '/'); ?>',
            currentPage: '<?php echo basename($_SERVER['PHP_SELF']); ?>',
            userId: <?php echo json_encode($_SESSION['admin_id'] ?? null); ?>,
            userName: <?php echo json_encode($_SESSION['admin_name'] ?? 'Admin'); ?>,
            userRole: <?php echo json_encode($_SESSION['admin_role'] ?? 'administrator'); ?>,
            csrfToken: '<?php echo $_SESSION['csrf_token'] ?? ''; ?>',
            ajaxUrl: 'ajax/',
            uploadUrl: 'upload/',
            mediaUrl: '../uploads/',
            debug: <?php echo (defined('DEBUG') && DEBUG) ? 'true' : 'false'; ?>
        };

        // Initialize admin panel when document is ready
        document.addEventListener('DOMContentLoaded', function() {
            // Initialize tooltips
            var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
            var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
                return new bootstrap.Tooltip(tooltipTriggerEl);
            });

            // Initialize popovers
            var popoverTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="popover"]'));
            var popoverList = popoverTriggerList.map(function (popoverTriggerEl) {
                return new bootstrap.Popover(popoverTriggerEl);
            });

            // Auto-dismiss alerts
            setTimeout(function() {
                var alerts = document.querySelectorAll('.alert:not(.alert-permanent)');
                alerts.forEach(function(alert) {
                    var bsAlert = new bootstrap.Alert(alert);
                    bsAlert.close();
                });
            }, 5000);

            // Global error handler
            window.addEventListener('error', function(e) {
                if (window.AdminConfig.debug) {
                    console.error('JavaScript Error:', e.error);
                }
            });

            // AJAX setup
            if (typeof $ !== 'undefined') {
                $.ajaxSetup({
                    headers: {
                        'X-CSRF-TOKEN': window.AdminConfig.csrfToken
                    },
                    error: function(xhr, status, error) {
                        if (xhr.status === 401) {
                            window.location.href = 'login.php';
                        } else if (xhr.status === 403) {
                            AdminPanel.showError('Access denied. You do not have permission to perform this action.');
                        } else if (xhr.status >= 500) {
                            AdminPanel.showError('Server error. Please try again later.');
                        }
                    }
                });
            }

            // Keyboard shortcuts
            document.addEventListener('keydown', function(e) {
                // Ctrl/Cmd + S to save (prevent default browser save)
                if ((e.ctrlKey || e.metaKey) && e.key === 's') {
                    e.preventDefault();
                    var saveBtn = document.querySelector('.btn-save, .btn-primary[type="submit"]');
                    if (saveBtn && !saveBtn.disabled) {
                        saveBtn.click();
                    }
                }

                // Escape to close modals
                if (e.key === 'Escape') {
                    var openModal = document.querySelector('.modal.show');
                    if (openModal) {
                        var modal = bootstrap.Modal.getInstance(openModal);
                        if (modal) {
                            modal.hide();
                        }
                    }
                }
            });

            // Form auto-save functionality
            var autoSaveForms = document.querySelectorAll('.auto-save');
            autoSaveForms.forEach(function(form) {
                var saveInterval = setInterval(function() {
                    if (form.checkValidity()) {
                        var formData = new FormData(form);
                        formData.append('auto_save', '1');
                        
                        fetch(form.action || window.location.href, {
                            method: 'POST',
                            body: formData,
                            headers: {
                                'X-CSRF-TOKEN': window.AdminConfig.csrfToken
                            }
                        })
                        .then(response => response.json())
                        .then(data => {
                            if (data.success) {
                                var indicator = document.getElementById('auto-save-indicator');
                                if (indicator) {
                                    indicator.textContent = 'Auto-saved at ' + new Date().toLocaleTimeString();
                                    indicator.style.display = 'block';
                                    setTimeout(() => {
                                        indicator.style.display = 'none';
                                    }, 3000);
                                }
                            }
                        })
                        .catch(error => {
                            console.error('Auto-save error:', error);
                        });
                    }
                }, 30000); // Auto-save every 30 seconds

                // Clear interval when form is submitted
                form.addEventListener('submit', function() {
                    clearInterval(saveInterval);
                });
            });

            // Image preview functionality
            var imageInputs = document.querySelectorAll('input[type="file"][accept*="image"]');
            imageInputs.forEach(function(input) {
                input.addEventListener('change', function(e) {
                    var file = e.target.files[0];
                    if (file) {
                        var reader = new FileReader();
                        reader.onload = function(e) {
                            var previewId = input.getAttribute('data-preview');
                            var preview = document.getElementById(previewId);
                            if (preview) {
                                preview.src = e.target.result;
                                preview.style.display = 'block';
                            }
                        };
                        reader.readAsDataURL(file);
                    }
                });
            });

            // Confirm delete functionality
            var deleteButtons = document.querySelectorAll('.btn-delete, .delete-btn');
            deleteButtons.forEach(function(btn) {
                btn.addEventListener('click', function(e) {
                    var message = this.getAttribute('data-confirm') || 'Are you sure you want to delete this item?';
                    if (!confirm(message)) {
                        e.preventDefault();
                        return false;
                    }
                });
            });

            // Copy to clipboard functionality
            var copyButtons = document.querySelectorAll('.btn-copy');
            copyButtons.forEach(function(btn) {
                btn.addEventListener('click', function() {
                    var target = this.getAttribute('data-target');
                    var text = target ? document.querySelector(target).textContent : this.getAttribute('data-text');
                    
                    if (navigator.clipboard) {
                        navigator.clipboard.writeText(text).then(function() {
                            AdminPanel.showSuccess('Copied to clipboard!');
                        });
                    } else {
                        // Fallback for older browsers
                        var textArea = document.createElement('textarea');
                        textArea.value = text;
                        document.body.appendChild(textArea);
                        textArea.select();
                        document.execCommand('copy');
                        document.body.removeChild(textArea);
                        AdminPanel.showSuccess('Copied to clipboard!');
                    }
                });
            });

            // Initialize page-specific functionality
            if (typeof initializePage === 'function') {
                initializePage();
            }

            console.log('Admin panel initialized successfully');
        });

        // Global utility functions
        function showLoading(message = 'Loading...') {
            var overlay = document.getElementById('loadingOverlay');
            var text = overlay.querySelector('.loading-text');
            if (text) {
                text.textContent = message;
            }
            overlay.style.display = 'flex';
        }

        function hideLoading() {
            var overlay = document.getElementById('loadingOverlay');
            overlay.style.display = 'none';
        }

        // AJAX form submission helper
        function submitFormAjax(form, successCallback, errorCallback) {
            var formData = new FormData(form);
            
            fetch(form.action || window.location.href, {
                method: 'POST',
                body: formData,
                headers: {
                    'X-CSRF-TOKEN': window.AdminConfig.csrfToken
                }
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    if (successCallback) {
                        successCallback(data);
                    } else {
                        AdminPanel.showSuccess(data.message || 'Operation completed successfully');
                    }
                } else {
                    if (errorCallback) {
                        errorCallback(data);
                    } else {
                        AdminPanel.showError(data.message || 'An error occurred');
                    }
                }
            })
            .catch(error => {
                console.error('Form submission error:', error);
                if (errorCallback) {
                    errorCallback({message: 'Network error occurred'});
                } else {
                    AdminPanel.showError('Network error occurred');
                }
            });
        }

        // Export global functions
        window.showLoading = showLoading;
        window.hideLoading = hideLoading;
        window.submitFormAjax = submitFormAjax;
    </script>

    <!-- Additional CSS for loading overlay -->
    <style>
        .loading-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.5);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 9999;
        }

        .loading-spinner {
            text-align: center;
            color: white;
        }

        .loading-text {
            margin-top: 1rem;
            font-size: 0.875rem;
        }

        .sidebar-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.5);
            z-index: 999;
            display: none;
        }

        @media (max-width: 991.98px) {
            .sidebar.show + .sidebar-overlay {
                display: block;
            }
        }

        #auto-save-indicator {
            position: fixed;
            bottom: 20px;
            right: 20px;
            background: var(--success-color);
            color: white;
            padding: 0.5rem 1rem;
            border-radius: var(--radius-lg);
            font-size: 0.875rem;
            z-index: 1000;
            display: none;
        }
    </style>

    <!-- Auto-save indicator -->
    <div id="auto-save-indicator"></div>

</body>
</html>
