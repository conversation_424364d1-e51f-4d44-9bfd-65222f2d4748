/* ===================================
   FLORI CONSTRUCTION - ADMIN PANEL
   Custom Admin Styles
   ================================== */

:root {
    /* Primary Colors */
    --primary-color: #2563eb;
    --primary-hover: #1d4ed8;
    --primary-light: #dbeafe;
    --primary-dark: #1e40af;

    /* Secondary Colors */
    --secondary-color: #64748b;
    --secondary-hover: #475569;
    --secondary-light: #f1f5f9;

    /* Accent Colors */
    --accent-color: #0ea5e9;
    --accent-hover: #0284c7;

    /* Status Colors */
    --success-color: #10b981;
    --success-light: #d1fae5;
    --warning-color: #f59e0b;
    --warning-light: #fef3c7;
    --danger-color: #ef4444;
    --danger-light: #fee2e2;
    --info-color: #06b6d4;
    --info-light: #cffafe;

    /* Background Colors */
    --body-bg: #eaf2fa !important;
    --sidebar-bg: #1e293b;
    --sidebar-hover: rgba(37, 99, 235, 0.1);
    --sidebar-active: #2563eb;
    --card-bg: #ffffff;
    --surface-bg: #f1f5f9;

    /* Text Colors */
    --text-primary: #0f172a;
    --text-secondary: #475569;
    --text-muted: #94a3b8;
    --text-light: #cbd5e1;
    --text-white: #ffffff;

    /* Border Colors */
    --border-color: #e2e8f0;
    --border-light: #f1f5f9;
    --border-dark: #cbd5e1;

    /* Shadows */
    --shadow-xs: 0 1px 2px 0 rgb(0 0 0 / 0.05);
    --shadow-sm: 0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1);
    --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
    --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
    --shadow-xl: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);

    /* Border Radius */
    --radius-sm: 0.375rem;
    --radius-md: 0.5rem;
    --radius-lg: 0.75rem;
    --radius-xl: 1rem;
    --radius-2xl: 1.5rem;

    /* Spacing */
    --sidebar-width: 280px;
    --header-height: 80px;

    /* Typography */
    --font-family-primary: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
    --font-family-heading: 'Poppins', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;

    /* Transitions */
    --transition-fast: 0.15s ease-in-out;
    --transition-normal: 0.3s ease-in-out;
    --transition-slow: 0.5s ease-in-out;
}

/* ===================================
   BASE STYLES
   ================================== */

* {
    box-sizing: border-box;
}

*::before,
*::after {
    box-sizing: border-box;
}

body {
    font-family: var(--font-family-primary);
    font-size: 0.875rem;
    line-height: 1.6;
    color: var(--text-primary);
    background-color: #eaf2fa;
    margin: 0;
    padding: 0;
    overflow-x: hidden;
}

/* ===================================
   TYPOGRAPHY
   ================================== */

h1,
h2,
h3,
h4,
h5,
h6 {
    font-family: var(--font-family-heading);
    font-weight: 600;
    line-height: 1.3;
    color: var(--text-primary);
    margin-bottom: 0.75rem;
}

h1 {
    font-size: 2rem;
    font-weight: 700;
}

h2 {
    font-size: 1.75rem;
    font-weight: 600;
}

h3 {
    font-size: 1.5rem;
    font-weight: 600;
}

h4 {
    font-size: 1.25rem;
    font-weight: 600;
}

h5 {
    font-size: 1.125rem;
    font-weight: 600;
}

h6 {
    font-size: 1rem;
    font-weight: 600;
}

p {
    margin-bottom: 1rem;
    color: var(--text-secondary);
}

a {
    color: var(--primary-color);
    text-decoration: none;
    transition: var(--transition-fast);
}

a:hover {
    color: var(--primary-hover);
    text-decoration: none;
}

/* ===================================
   LAYOUT STRUCTURE
   ================================== */

.admin-wrapper {
    display: flex;
    min-height: 100vh;
}

.sidebar {
    position: fixed;
    top: 0;
    left: 0;
    width: var(--sidebar-width);
    height: 100vh;
    background: var(--sidebar-bg);
    z-index: 1050;
    transition: var(--transition-normal);
    overflow-y: auto;
    box-shadow: var(--shadow-lg);
    display: flex;
    flex-direction: column;
    scrollbar-width: thin;
    scrollbar-color: rgba(255, 255, 255, 0.2) transparent;
}

/* Custom scrollbar for webkit browsers */
.sidebar::-webkit-scrollbar {
    width: 6px;
}

.sidebar::-webkit-scrollbar-track {
    background: transparent;
}

.sidebar::-webkit-scrollbar-thumb {
    background: rgba(255, 255, 255, 0.2);
    border-radius: 3px;
}

.sidebar::-webkit-scrollbar-thumb:hover {
    background: rgba(255, 255, 255, 0.3);
}

.main-content {
    flex: 1;
    margin-left: var(--sidebar-width);
    min-height: 100vh;
    display: flex;
    flex-direction: column;
    transition: var(--transition-normal);
}

.top-header {
    position: sticky;
    top: 0;
    height: var(--header-height);
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border-bottom: 1px solid var(--border-light);
    box-shadow: var(--shadow-sm);
    z-index: 999;
    display: flex;
    align-items: center;
    padding: 0 2rem;
}

.content-area {
    flex: 1;
    padding: 2rem;
}

/* ===================================
   SIDEBAR STYLES
   ================================== */

.sidebar-brand {
    display: flex;
    align-items: center;
    padding: 1.75rem 2rem;
    border-bottom: 1px solid rgba(255, 255, 255, 0.15);
    margin-bottom: 0;
    flex-shrink: 0;
    background: rgba(0, 0, 0, 0.1);
    backdrop-filter: blur(10px);
}

.brand-icon {
    width: 48px;
    height: 48px;
    background: linear-gradient(135deg, var(--primary-color), var(--accent-color));
    border-radius: var(--radius-lg);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1.25rem;
    margin-right: 1rem;
    box-shadow: 0 4px 16px rgba(37, 99, 235, 0.3);
    border: 2px solid rgba(255, 255, 255, 0.1);
    transition: all var(--transition-fast);
}

.sidebar-brand:hover .brand-icon {
    transform: scale(1.05);
    box-shadow: 0 6px 20px rgba(37, 99, 235, 0.4);
}

.brand-text h5 {
    color: white;
    font-weight: 700;
    margin: 0 0 0.25rem 0;
    font-size: 1.125rem;
}

.brand-text small {
    color: rgba(255, 255, 255, 0.7);
    font-size: 0.8125rem;
    font-weight: 500;
}

.sidebar-nav {
    flex: 1;
    padding: 0rem 1rem 0rem;
    overflow-y: auto;
    display: flex;
    flex-direction: column;
}

.nav-section {
    margin-bottom: 1.5rem;
}

.nav-section:not(:last-child) {
    border-bottom: 1px solid rgba(255, 255, 255, 0.08);
    padding-bottom: 1rem;
    margin-bottom: 1.5rem;
}

.nav-section-title {
    font-size: 0.75rem;
    font-weight: 700;
    text-transform: uppercase;
    letter-spacing: 0.1em;
    color: rgba(255, 255, 255, 0.6);
    margin: 0 1rem 0.875rem;
    padding-bottom: 0.5rem;
    position: relative;
}

.nav-link {
    display: flex;
    align-items: center;
    padding: 0.875rem 1rem;
    margin: 0.125rem 0;
    color: rgba(255, 255, 255, 0.8);
    border-radius: var(--radius-lg);
    transition: all var(--transition-fast);
    font-weight: 500;
    text-decoration: none;
    position: relative;
    border: 1px solid transparent;
    backdrop-filter: blur(5px);
}

.nav-link:hover {
    color: white;
    background: rgba(37, 99, 235, 0.15);
    border-color: rgba(37, 99, 235, 0.3);
    transform: translateX(6px);
    text-decoration: none;
    box-shadow: 0 4px 12px rgba(37, 99, 235, 0.15);
}

.nav-link.active {
    color: white;
    background: linear-gradient(135deg, var(--primary-color), var(--accent-color));
    border-color: var(--primary-color);
    box-shadow: 0 4px 16px rgba(37, 99, 235, 0.3);
    transform: translateX(4px);
}

.nav-link.active::before {
    content: '';
    position: absolute;
    left: -1rem;
    top: 50%;
    transform: translateY(-50%);
    width: 4px;
    height: 32px;
    background: linear-gradient(135deg, #ffffff, #e2e8f0);
    border-radius: 0 4px 4px 0;
    box-shadow: 0 2px 8px rgba(255, 255, 255, 0.2);
}

.nav-link i {
    margin-right: 0.875rem;
    width: 20px;
    font-size: 1rem;
    text-align: center;
    flex-shrink: 0;
    transition: all var(--transition-fast);
}

.nav-link:hover i {
    transform: scale(1.1);
}

.nav-link.active i {
    transform: scale(1.05);
}

.nav-badge {
    margin-left: auto;
    font-size: 0.6875rem;
    padding: 0.25rem 0.5rem;
    border-radius: var(--radius-md);
    font-weight: 600;
    min-width: 20px;
    height: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
}

/* ===================================
   SIDEBAR FOOTER
   ================================== */

.sidebar-footer {
    flex-shrink: 0;
    padding: 1.5rem 1rem 2rem;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    background: rgba(0, 0, 0, 0.1);
    backdrop-filter: blur(10px);
}

.sidebar-actions {
    display: flex;
    flex-direction: column;
    gap: 0.75rem;
}

.sidebar-btn {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 0.75rem 1rem;
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: var(--radius-lg);
    background: rgba(255, 255, 255, 0.05);
    color: rgba(255, 255, 255, 0.9);
    text-decoration: none;
    font-weight: 500;
    font-size: 0.875rem;
    transition: all var(--transition-fast);
    backdrop-filter: blur(5px);
    position: relative;
    overflow: hidden;
}

.sidebar-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
    transition: left 0.5s ease;
}

.sidebar-btn:hover::before {
    left: 100%;
}

.sidebar-btn:hover {
    background: rgba(255, 255, 255, 0.1);
    border-color: rgba(255, 255, 255, 0.3);
    color: white;
    text-decoration: none;
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
}

.sidebar-btn.btn-primary {
    background: linear-gradient(135deg, var(--primary-color), var(--accent-color));
    border-color: var(--primary-color);
    color: white;
}

.sidebar-btn.btn-primary:hover {
    background: linear-gradient(135deg, var(--primary-hover), var(--accent-hover));
    border-color: var(--primary-hover);
    box-shadow: 0 4px 16px rgba(37, 99, 235, 0.3);
}

.sidebar-btn i {
    margin-right: 0.5rem;
    font-size: 0.875rem;
}

.sidebar-divider {
    height: 1px;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    margin: 1rem 0;
}

.sidebar-website-link {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 0.625rem 1rem;
    color: rgba(255, 255, 255, 0.7);
    text-decoration: none;
    font-size: 0.8125rem;
    border-radius: var(--radius-md);
    transition: all var(--transition-fast);
    border: 1px solid transparent;
}

.sidebar-website-link:hover {
    color: rgba(255, 255, 255, 0.9);
    background: rgba(255, 255, 255, 0.05);
    border-color: rgba(255, 255, 255, 0.1);
    text-decoration: none;
    transform: translateY(-1px);
}

.sidebar-website-link i {
    margin-right: 0.5rem;
    font-size: 0.75rem;
}

/* ===================================
   SIDEBAR ANIMATIONS & EFFECTS
   ================================== */

.sidebar-nav .nav-section {
    animation: fadeInUp 0.3s ease-out;
}

.sidebar-nav .nav-section:nth-child(1) {
    animation-delay: 0.1s;
}

.sidebar-nav .nav-section:nth-child(2) {
    animation-delay: 0.2s;
}

.sidebar-nav .nav-section:nth-child(3) {
    animation-delay: 0.3s;
}

.sidebar-nav .nav-section:nth-child(4) {
    animation-delay: 0.4s;
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }

    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.nav-link {
    animation: slideInLeft 0.3s ease-out;
}

@keyframes slideInLeft {
    from {
        opacity: 0;
        transform: translateX(-20px);
    }

    to {
        opacity: 1;
        transform: translateX(0);
    }
}

/* Pulse effect for badges */
.nav-badge {
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% {
        box-shadow: 0 0 0 0 rgba(239, 68, 68, 0.7);
    }

    70% {
        box-shadow: 0 0 0 10px rgba(239, 68, 68, 0);
    }

    100% {
        box-shadow: 0 0 0 0 rgba(239, 68, 68, 0);
    }
}

/* Hover effect for nav sections */
.nav-section:hover .nav-section-title {
    color: rgba(255, 255, 255, 0.8);
    transform: translateX(2px);
}

/* Focus states for accessibility */
.nav-link:focus {
    outline: 2px solid var(--primary-color);
    outline-offset: 2px;
}

.sidebar-btn:focus {
    outline: 2px solid rgba(255, 255, 255, 0.5);
    outline-offset: 2px;
}

/* ===================================
   HEADER STYLES
   ================================== */

.header-content {
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: 100%;
}

.header-left {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.mobile-toggle {
    display: none;
    background: none;
    border: none;
    padding: 0.5rem;
    border-radius: var(--radius-md);
    color: var(--text-secondary);
    cursor: pointer;
}

.page-title {
    font-size: 1.5rem;
    font-weight: 600;
    color: var(--text-primary);
    margin: 0;
}

.breadcrumb {
    font-size: 0.875rem;
    color: var(--text-muted);
    margin: 0;
}

.header-right {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.search-box {
    position: relative;
    width: 300px;
}

.search-input {
    width: 100%;
    padding: 0.75rem 1rem 0.75rem 2.5rem;
    border: 1px solid var(--border-color);
    border-radius: var(--radius-lg);
    background: var(--card-bg);
    font-size: 0.875rem;
    transition: var(--transition-fast);
}

.search-input:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
}

.search-icon {
    position: absolute;
    left: 0.875rem;
    top: 50%;
    transform: translateY(-50%);
    color: var(--text-muted);
    font-size: 0.875rem;
}

.header-actions {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.action-btn {
    width: 40px;
    height: 40px;
    border-radius: var(--radius-lg);
    border: 1px solid var(--border-color);
    background: var(--card-bg);
    color: var(--text-secondary);
    display: flex;
    align-items: center;
    justify-content: center;
    transition: var(--transition-fast);
    position: relative;
    cursor: pointer;
    text-decoration: none;
}

.action-btn:hover {
    background: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
    transform: translateY(-1px);
    box-shadow: var(--shadow-md);
    text-decoration: none;
}

.notification-badge {
    position: absolute;
    top: -4px;
    right: -4px;
    background: var(--danger-color);
    color: white;
    border-radius: 50%;
    width: 16px;
    height: 16px;
    font-size: 0.6rem;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 600;
    border: 2px solid white;
}

.user-dropdown {
    position: relative;
}

.user-toggle {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    padding: 0.5rem 1rem;
    background: var(--card-bg);
    border: 1px solid var(--border-color);
    border-radius: var(--radius-lg);
    transition: var(--transition-fast);
    cursor: pointer;
    text-decoration: none;
    color: var(--text-primary);
}

.user-toggle:hover {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
    text-decoration: none;
    color: var(--text-primary);
}

.user-avatar {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    object-fit: cover;
    border: 2px solid var(--border-color);
}

.user-info {
    text-align: left;
    line-height: 1.3;
}

.user-name {
    font-weight: 600;
    font-size: 0.875rem;
    color: var(--text-primary);
    margin: 0;
}

.user-role {
    font-size: 0.75rem;
    color: var(--text-secondary);
    margin: 0;
}

.dropdown-arrow {
    color: var(--text-muted);
    font-size: 0.75rem;
    transition: transform var(--transition-fast);
}

.user-toggle[aria-expanded="true"] .dropdown-arrow {
    transform: rotate(180deg);
}

/* ===================================
   DROPDOWN STYLES
   ================================== */

.dropdown-menu {
    border: none;
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-xl);
    padding: 0.5rem 0;
    min-width: 280px;
    background: var(--card-bg);
    margin-top: 0.5rem;
}

.dropdown-item {
    padding: 0.75rem 1.25rem;
    font-size: 0.875rem;
    transition: var(--transition-fast);
    color: var(--text-primary);
    display: flex;
    align-items: center;
    gap: 0.75rem;
}

.dropdown-item:hover {
    background: var(--surface-bg);
    color: var(--text-primary);
}

.dropdown-item:active {
    background: var(--primary-color);
    color: white;
}

.dropdown-divider {
    margin: 0.5rem 0;
    border-color: var(--border-light);
}

.dropdown-header {
    padding: 1rem 1.25rem 0.5rem;
    font-size: 0.75rem;
    font-weight: 700;
    text-transform: uppercase;
    letter-spacing: 0.05em;
    color: var(--text-muted);
}

/* ===================================
   CARD STYLES
   ================================== */

.card {
    background: var(--card-bg);
    border: 1px solid var(--border-light);
    border-radius: var(--radius-xl);
    box-shadow: var(--shadow-sm);
    transition: var(--transition-normal);
    overflow: hidden;
    margin-bottom: 1.5rem;
}

.card:hover {
    box-shadow: var(--shadow-md);
    transform: translateY(-2px);
}

.card-header {
    background: var(--surface-bg);
    border-bottom: 1px solid var(--border-light);
    padding: 1.5rem 2rem;
    font-weight: 600;
    color: var(--text-primary);
    font-size: 1rem;
}

.card-body {
    padding: 2rem;
}

.card-footer {
    background: var(--surface-bg);
    border-top: 1px solid var(--border-light);
    padding: 1rem 2rem;
}

/* ===================================
   STATISTICS CARDS
   ================================== */

.stats-card {
    background: var(--card-bg);
    border: 1px solid var(--border-light);
    border-radius: var(--radius-xl);
    padding: 2rem;
    transition: var(--transition-normal);
    position: relative;
    overflow: hidden;
    height: 100%;
    margin-bottom: 1.5rem;
}

.stats-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, var(--primary-color), var(--accent-color));
}

.stats-card:hover {
    transform: translateY(-4px);
    box-shadow: var(--shadow-lg);
}

.stats-icon {
    width: 56px;
    height: 56px;
    border-radius: var(--radius-lg);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    margin-bottom: 1rem;
}

.stats-number {
    font-size: 2.5rem;
    font-weight: 700;
    color: var(--text-primary);
    margin-bottom: 0.5rem;
    line-height: 1;
}

.stats-label {
    font-size: 0.875rem;
    font-weight: 600;
    color: var(--text-secondary);
    text-transform: uppercase;
    letter-spacing: 0.05em;
    line-height: 1.3;
}

.stats-change {
    font-size: 0.75rem;
    font-weight: 600;
    padding: 0.25rem 0.5rem;
    border-radius: var(--radius-sm);
    margin-top: 0.5rem;
    display: inline-block;
}

.stats-change.positive {
    background: var(--success-light);
    color: var(--success-color);
}

.stats-change.negative {
    background: var(--danger-light);
    color: var(--danger-color);
}

/* Color Variants for Stats */
.stats-primary .stats-icon {
    background: linear-gradient(135deg, var(--primary-color), var(--primary-hover));
    color: white;
}

.stats-success .stats-icon {
    background: linear-gradient(135deg, var(--success-color), #059669);
    color: white;
}

.stats-warning .stats-icon {
    background: linear-gradient(135deg, var(--warning-color), #d97706);
    color: white;
}

.stats-info .stats-icon {
    background: linear-gradient(135deg, var(--info-color), var(--accent-hover));
    color: white;
}

/* ===================================
   BUTTON STYLES
   ================================== */

.btn {
    font-weight: 500;
    border-radius: var(--radius-lg);
    transition: var(--transition-normal);
    font-size: 0.875rem;
    padding: 0.75rem 1.5rem;
    border: 1px solid transparent;
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    text-decoration: none;
    line-height: 1.4;
    cursor: pointer;
}

.btn:focus {
    outline: none;
    box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.2);
}

.btn-primary {
    background: linear-gradient(135deg, var(--primary-color), var(--primary-hover));
    color: white;
    border-color: var(--primary-color);
    box-shadow: var(--shadow-sm);
}

.btn-primary:hover {
    background: linear-gradient(135deg, var(--primary-hover), var(--primary-dark));
    transform: translateY(-2px);
    box-shadow: var(--shadow-md);
    color: white;
    text-decoration: none;
}

.btn-secondary {
    background: var(--secondary-color);
    color: white;
    border-color: var(--secondary-color);
}

.btn-secondary:hover {
    background: var(--secondary-hover);
    border-color: var(--secondary-hover);
    transform: translateY(-1px);
    color: white;
    text-decoration: none;
}

.btn-outline-primary {
    background: transparent;
    color: var(--primary-color);
    border-color: var(--primary-color);
}

.btn-outline-primary:hover {
    background: var(--primary-color);
    color: white;
    transform: translateY(-1px);
    text-decoration: none;
}

.btn-success {
    background: var(--success-color);
    color: white;
    border-color: var(--success-color);
}

.btn-success:hover {
    background: #059669;
    border-color: #059669;
    transform: translateY(-1px);
    color: white;
    text-decoration: none;
}

.btn-warning {
    background: var(--warning-color);
    color: white;
    border-color: var(--warning-color);
}

.btn-warning:hover {
    background: #d97706;
    border-color: #d97706;
    transform: translateY(-1px);
    color: white;
    text-decoration: none;
}

.btn-danger {
    background: var(--danger-color);
    color: white;
    border-color: var(--danger-color);
}

.btn-danger:hover {
    background: #dc2626;
    border-color: #dc2626;
    transform: translateY(-1px);
    color: white;
    text-decoration: none;
}

.btn-sm {
    padding: 0.5rem 1rem;
    font-size: 0.8125rem;
}

.btn-lg {
    padding: 1rem 2rem;
    font-size: 1rem;
}

/* ===================================
   FORM STYLES
   ================================== */

.form-control {
    border: 1px solid var(--border-color);
    border-radius: var(--radius-lg);
    padding: 0.75rem 1rem;
    font-size: 0.875rem;
    transition: var(--transition-fast);
    background: var(--card-bg);
    line-height: 1.5;
}

.form-control:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
}

.form-label {
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: 0.5rem;
    font-size: 0.875rem;
    line-height: 1.4;
}

.form-select {
    border: 1px solid var(--border-color);
    border-radius: var(--radius-lg);
    padding: 0.75rem 1rem;
    background: var(--card-bg);
    transition: var(--transition-fast);
    font-size: 0.875rem;
}

.form-select:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
}

.input-group {
    border-radius: var(--radius-lg);
    overflow: hidden;
}

.input-group-text {
    background: var(--surface-bg);
    border: 1px solid var(--border-color);
    color: var(--text-secondary);
}

/* ===================================
   TABLE STYLES
   ================================== */

.table {
    border-collapse: separate;
    border-spacing: 0;
    background: var(--card-bg);
    border-radius: var(--radius-lg);
    overflow: hidden;
}

.table thead th {
    background: var(--surface-bg);
    border-bottom: 2px solid var(--border-light);
    font-weight: 600;
    font-size: 0.8125rem;
    text-transform: uppercase;
    letter-spacing: 0.05em;
    color: var(--text-secondary);
    padding: 1.25rem 1.5rem;
    border-top: none;
    line-height: 1.4;
}

.table tbody td {
    padding: 1.25rem 1.5rem;
    border-bottom: 1px solid var(--border-light);
    vertical-align: middle;
    color: var(--text-primary);
    font-size: 0.875rem;
    line-height: 1.5;
}

.table tbody tr:hover {
    background: var(--surface-bg);
}

.table tbody tr:last-child td {
    border-bottom: none;
}

.table-responsive {
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-sm);
    border: 1px solid var(--border-light);
    margin-bottom: 1.5rem;
}

/* ===================================
   BADGE STYLES
   ================================== */

.badge {
    font-weight: 500;
    font-size: 0.75rem;
    padding: 0.375rem 0.75rem;
    border-radius: var(--radius-md);
    text-transform: uppercase;
    letter-spacing: 0.025em;
}

.badge.bg-success {
    background: var(--success-color) !important;
    color: white;
}

.badge.bg-warning {
    background: var(--warning-color) !important;
    color: white;
}

.badge.bg-info {
    background: var(--info-color) !important;
    color: white;
}

.badge.bg-danger {
    background: var(--danger-color) !important;
    color: white;
}

.badge.bg-primary {
    background: var(--primary-color) !important;
    color: white;
}

.badge.bg-secondary {
    background: var(--secondary-color) !important;
    color: white;
}

/* ===================================
   ALERT STYLES
   ================================== */

.alert {
    border: none;
    border-radius: var(--radius-lg);
    padding: 1rem 1.25rem;
    margin-bottom: 1.5rem;
    border-left: 4px solid;
    box-shadow: var(--shadow-sm);
}

.alert-success {
    background: var(--success-light);
    color: #065f46;
    border-left-color: var(--success-color);
}

.alert-danger {
    background: var(--danger-light);
    color: #991b1b;
    border-left-color: var(--danger-color);
}

.alert-warning {
    background: var(--warning-light);
    color: #92400e;
    border-left-color: var(--warning-color);
}

.alert-info {
    background: var(--info-light);
    color: #1e40af;
    border-left-color: var(--info-color);
}

/* ===================================
   UTILITY CLASSES
   ================================== */

.text-xs {
    font-size: 0.75rem;
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 0.05em;
}

.shadow-sm {
    box-shadow: var(--shadow-sm);
}

.shadow {
    box-shadow: var(--shadow-md);
}

.shadow-lg {
    box-shadow: var(--shadow-lg);
}

.rounded-lg {
    border-radius: var(--radius-lg);
}

.rounded-xl {
    border-radius: var(--radius-xl);
}

/* Enhanced Spacing Utilities */
.mb-4 {
    margin-bottom: 2rem !important;
}

.mb-5 {
    margin-bottom: 2.5rem !important;
}

.mt-4 {
    margin-top: 2rem !important;
}

.mt-5 {
    margin-top: 2.5rem !important;
}

/* ===================================
   RESPONSIVE DESIGN
   ================================== */

/* Mobile Styles */
@media (max-width: 991.98px) {
    .sidebar {
        transform: translateX(-100%);
        z-index: 1060;
    }

    .sidebar.show {
        transform: translateX(0);
        box-shadow: var(--shadow-xl);
    }

    .main-content {
        margin-left: 0;
    }

    .mobile-toggle {
        display: block !important;
    }

    .search-box {
        width: 200px;
    }

    .header-actions {
        gap: 0.25rem;
    }

    .user-info {
        display: none;
    }

    .content-area {
        padding: 1rem;
    }

    .page-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 1rem;
    }

    .stats-card {
        margin-bottom: 1rem;
    }
}

@media (max-width: 767.98px) {
    .search-box {
        display: none;
    }

    .header-actions .action-btn {
        width: 36px;
        height: 36px;
    }

    .user-toggle {
        padding: 0.375rem 0.75rem;
    }

    .content-area {
        padding: 0.75rem;
    }

    .top-header {
        padding: 0 1rem;
        height: 60px;
    }

    .page-title {
        font-size: 1.25rem;
    }
}

@media (max-width: 575.98px) {
    .header-actions {
        display: none;
    }

    .user-toggle .dropdown-arrow {
        display: none;
    }

    .stats-card {
        text-align: center;
    }

    .card-header {
        padding: 0.75rem 1rem;
    }

    .card-body {
        padding: 0.75rem 1rem;
    }
}

@media (max-width: 1199.98px) {
    .search-box {
        width: 250px;
    }
}

@media (max-width: 991.98px) {
    .sidebar {
        transform: translateX(-100%);
        transition: transform var(--transition-normal);
    }

    .sidebar.show {
        transform: translateX(0);
    }

    .main-content {
        margin-left: 0;
    }

    .mobile-toggle {
        display: block;
    }

    .search-box {
        display: none;
    }

    .content-area {
        padding: 1.5rem;
    }

    .stats-card {
        padding: 1.5rem;
        margin-bottom: 1.25rem;
    }

    .card-body {
        padding: 1.5rem;
    }
}

@media (max-width: 767.98px) {
    .content-area {
        padding: 1rem;
    }

    .stats-card {
        padding: 1.25rem;
        margin-bottom: 1rem;
    }

    .stats-number {
        font-size: 2rem;
        margin-bottom: 0.5rem;
    }

    .stats-icon {
        width: 48px;
        height: 48px;
        font-size: 1.25rem;
        margin-bottom: 0.75rem;
    }

    .card-body {
        padding: 1.25rem;
    }

    .table thead th,
    .table tbody td {
        padding: 1rem 1.25rem;
        font-size: 0.8125rem;
    }

    .btn {
        padding: 0.625rem 1.25rem;
        font-size: 0.8125rem;
    }

    .header-actions {
        gap: 0.5rem;
    }

    .action-btn {
        width: 36px;
        height: 36px;
    }

    .top-header {
        height: 70px;
        padding: 0 1rem;
    }

    .page-title {
        font-size: 1.25rem;
    }
}

@media (max-width: 575.98px) {
    .content-area {
        padding: 0.75rem;
    }

    .stats-card {
        padding: 1rem;
        margin-bottom: 1rem;
    }

    .stats-number {
        font-size: 1.75rem;
    }

    .stats-icon {
        width: 40px;
        height: 40px;
        font-size: 1rem;
        margin-bottom: 0.5rem;
    }

    .card-body {
        padding: 1rem;
    }

    .card-header {
        padding: 1rem 1.25rem;
    }

    .table thead th,
    .table tbody td {
        padding: 0.75rem 1rem;
        font-size: 0.75rem;
    }

    .btn {
        padding: 0.5rem 1rem;
        font-size: 0.75rem;
    }

    .top-header {
        height: 65px;
        padding: 0 0.75rem;
    }

    .page-title {
        font-size: 1.125rem;
    }

    .user-toggle {
        padding: 0.375rem 0.75rem;
    }

    .user-name {
        font-size: 0.8125rem;
    }

    .user-role {
        font-size: 0.6875rem;
    }
}

/* ===================================
   ANIMATION CLASSES
   ================================== */

.fade-in {
    animation: fadeIn 0.3s ease-in-out;
}

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(10px);
    }

    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.slide-in {
    animation: slideIn 0.3s ease-in-out;
}

@keyframes slideIn {
    from {
        opacity: 0;
        transform: translateX(-20px);
    }

    to {
        opacity: 1;
        transform: translateX(0);
    }
}

/* Loading States */
.loading {
    opacity: 0.6;
    pointer-events: none;
}

.spinner {
    animation: spin 1s linear infinite;
}

@keyframes spin {
    from {
        transform: rotate(0deg);
    }

    to {
        transform: rotate(360deg);
    }
}